import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../../../styles/styled'
import {
  dayjsFormat,
  getKeysFromObjects,
  getNameFrom_Id,
  getValueByKeyAndMatch,
  isSuccess,
  notify,
} from '../../../../../shared/helpers/util'

import { IntendWidth } from '../../../style'
import { InputWithValidation } from '../../../../../shared/inputWithValidation/InputWithValidation'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../../../../shared/date/SharedDate'
import { SharedDateAndTime } from '../../../../../shared/date/SharedDateAndTime'
import { Form, Formik } from 'formik'
import AutoCompleteIndentation from '../../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import { mergeSourceAndCampaignNames } from '../../addNewContactModal/AddNewContactModal'
import {
  getFormattedLeadSrcData,
  getLeadSrcDropdownId,
  getLeadSrcDropdownName,
} from '../../../../leadSource/LeadSource'
import { updateLead } from '../../../../../logic/apis/contact'

interface LeadDateSectionProps {
  key: string
  lead: any
  leadIndex: number
  leadSrcData: any[]
  refererres: any[]
  projectTypesDrop: any[]
  toggleCount: (heading: string) => void
  toggleHeading: any
  initFetch: any
  renderInvalidButton: React.ReactNode
  renderLostButton: React.ReactNode
  leads: any[]
  setShowCreateRuleDialog: (value: React.SetStateAction<boolean>) => void
  setTrackingRuleId: (value: React.SetStateAction<string>) => void
  setSelectedLeadId: (value: React.SetStateAction<string>) => void
}
const LeadInfo: React.FC<LeadDateSectionProps> = ({
  key,
  leadIndex,
  lead,
  leadSrcData,
  refererres,
  projectTypesDrop,
  toggleCount,
  toggleHeading,
  initFetch,
  renderInvalidButton,
  renderLostButton,
  leads,
  setTrackingRuleId,
  setShowCreateRuleDialog,
  setSelectedLeadId,
}) => {
  const initialValues = {
    newLeadDate: lead ? dayjsFormat(lead?.newLeadDate, 'YYYY-MM-DDTHH:mm') : '',
    leadSourceId: lead ? lead?.leadSourceId : '',
    trackingRuleId: lead ? lead?.trackingRuleId : '',
    leadSourceName: lead ? getLeadSrcDropdownName(lead?.campaignId || lead?.leadSourceId, leadSrcData)?.sourceName : '',
    referredBy: lead ? getValueByKeyAndMatch('name', lead?.referredBy, '_id', refererres) : '',
    workType: lead ? getValueByKeyAndMatch('name', lead?.workType, 'id', projectTypesDrop) : '',
    invalidLeadReason: lead ? lead?.invalidLeadReason : '',
    invalidLeadNote: lead ? lead?.invalidLeadNote : '',
    lostDate: lead ? dayjsFormat(lead?.lostDate, 'YYYY-MM-DDTHH:mm') : '',
    lostReason: lead ? lead?.lostReason : '',
    lostNote: lead ? lead?.lostNote : '',
  }
  const [selectedLeadSourceObject, setSelectedLeadSourceObject] = useState()

  const handleInputBlurValue = async (data: any) => {
    try {
      const hasValues = Object.values(data)?.filter(Boolean)
      if (hasValues.length) {
        const res = await updateLead(data, lead?._id)
        if (isSuccess(res)) {
          notify('Lead updated!', 'success')
          initFetch()
          // data.leadSourceName && (lead.leadSourceName = data.leadSourceName)
          // data.referredBy && (lead.referredBy = getValueByKeyAndMatch('name', data?.referredBy, '_id', refererres))
          // data.workType && (lead.workType = getValueByKeyAndMatch('name', data?.workType, 'id', projectTypesDrop))
          // data.newLeadDate && (lead.newLeadDate = dayjsFormat(data?.newLeadDate, 'YYYY-MM-DDTHH:mm'))
        }
      }
    } catch (error) {
      console.log({ error })
    }
  }
  return (
    <SharedStyled.FlexCol padding="0 0 0 25px">
      <>
        <SharedStyled.Text variant="link" fontWeight="700" fontSize="18px" margin="10px auto 0 0">
          {' '}
          <span onClick={() => toggleCount(`${leadIndex}`)}>
            {!toggleHeading[`${leadIndex}`] ? <>&#9654;</> : <>&#9660;</>}
            &nbsp; {dayjsFormat(lead?.newLeadDate, 'M/D/YY')}
          </span>
        </SharedStyled.Text>

        {toggleHeading[`${leadIndex}`] && (
          <Formik
            initialValues={initialValues}
            // validationSchema={validationSchema}
            onSubmit={() => {}}
            enableReinitialize={true}
          >
            {({ values, errors, touched, setFieldValue }) => {
              useEffect(() => {
                if (values.leadSourceName !== '') {
                  const result = getLeadSrcDropdownId(values.leadSourceName || '', leadSrcData)
                  setSelectedLeadSourceObject(result.leadSourceObject)
                }
              }, [values.leadSourceName])

              return (
                <Form style={{ width: '100%' }}>
                  <SharedStyled.FlexCol gap="10px">
                    <SharedDateAndTime
                      value={values?.newLeadDate || ''}
                      labelName="Lead Date"
                      stateName="newLeadDate"
                      error={!!(touched?.newLeadDate && errors?.newLeadDate)}
                      setFieldValue={setFieldValue}
                      onBlur={() => {
                        // if (values?.newLeadDate === lead.newLeadDate) return
                        handleInputBlurValue({ newLeadDate: new Date(values?.newLeadDate) })
                      }}
                    />

                    <div>
                      {
                        <>
                          {!!!values?.leadSourceId ? (
                            <SharedStyled.Text
                              color="red"
                              variant="link"
                              textDecoration="underline"
                              onClick={() => {
                                setShowCreateRuleDialog(true)
                                setSelectedLeadId(lead?._id)
                              }}
                            >
                              No Lead Source! Click to Create
                            </SharedStyled.Text>
                          ) : values?.trackingRuleId ? (
                            <SharedStyled.Text
                              color="green"
                              variant="link"
                              textDecoration="underline"
                              onClick={() => {
                                setTrackingRuleId(values?.trackingRuleId)
                                setSelectedLeadId(lead?._id)
                                setShowCreateRuleDialog(true)
                              }}
                            >
                              Lead Source Matched! Click to Edit
                            </SharedStyled.Text>
                          ) : (
                            <></>
                          )}
                        </>
                      }
                      {leadSrcData?.length ? (
                        <AutoCompleteIndentation
                          labelName="Lead Source"
                          stateName={`leadSourceName`}
                          isLeadSource
                          dropdownHeight="180px"
                          error={touched.leadSourceName && errors.leadSourceName ? true : false}
                          borderRadius="0px"
                          setFieldValue={setFieldValue}
                          options={mergeSourceAndCampaignNames(leadSrcData)}
                          formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                          value={values.leadSourceName!}
                          setValueOnClick={(val: string) => {
                            setFieldValue('leadSourceName', val)
                          }}
                          className="material-autocomplete"
                          isIndentation={true}
                          onBlur={(name: string) => {
                            // if (name === lead.leadSourceName) {
                            //   return
                            // }

                            const result = getLeadSrcDropdownId(name || '', leadSrcData)
                            const leadSourceId = result?.leadSourceId
                            const campaignId = result?.campaignId || null
                            console.log({ result })
                            const data = {
                              leadSourceId,
                              campaignId,
                            }
                            handleInputBlurValue(data)
                          }}
                        />
                      ) : null}
                      {selectedLeadSourceObject?.code === 'referral' && (
                        <SharedStyled.FlexBox width="100%" justifyContent="end">
                          <CustomSelect
                            labelName="Referrer"
                            stateName="referredBy"
                            error={touched.referredBy && errors.referredBy ? true : false}
                            setFieldValue={setFieldValue}
                            setValue={() => {}}
                            value={values.referredBy}
                            dropDownData={getKeysFromObjects(refererres, 'name')}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                            maxWidth="95%"
                            onBlur={() => {
                              // if (values?.referredBy === lead.referredBy) return
                              const referredBy = getValueByKeyAndMatch('_id', values.referredBy, 'name', refererres)
                              handleInputBlurValue({ referredBy })
                            }}
                          />
                        </SharedStyled.FlexBox>
                      )}
                    </div>

                    <CustomSelect
                      labelName="Work Type"
                      stateName="workType"
                      value={values?.workType || ''}
                      error={!!(touched?.workType && errors?.workType)}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name)]}
                      innerHeight="52px"
                      margin="10px 0 0 0"
                      onBlur={() => {
                        // if (values?.workType === lead.workType) return
                        const workType = getValueByKeyAndMatch('id', values.workType, 'name', projectTypesDrop)
                        handleInputBlurValue({ workType })
                      }}
                    />

                    <SharedStyled.FlexRow alignItems="center">
                      <CustomSelect
                        labelName="Invalid Lead Reason"
                        stateName="invalidLeadReason"
                        value={values?.invalidLeadReason || ''}
                        error={!!(touched?.invalidLeadReason && errors?.invalidLeadReason)}
                        setFieldValue={setFieldValue}
                        setValue={() => {}}
                        disabled={true}
                        dropDownData={
                          [
                            // '',
                            // 'Service Not Provided',
                            // 'Outside Service Area',
                            // 'Purchase Material Only',
                            // 'Looking For Work',
                            // 'Unreachable',
                            // 'Spam',
                            // 'Other (Describe in notes)',
                          ]
                        }
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />
                      <div style={{ marginTop: '10px' }}>{renderInvalidButton}</div>
                    </SharedStyled.FlexRow>

                    <IntendWidth>
                      {values?.invalidLeadReason === 'Other (Describe in notes)' && (
                        <InputWithValidation
                          labelName="Invalid Lead Notes"
                          stateName="invalidLeadNote"
                          disabled={true}
                          error={touched.invalidLeadNote && errors.invalidLeadNote ? true : false}
                        />
                      )}
                    </IntendWidth>

                    <SharedStyled.FlexRow alignItems="center">
                      <SharedDate
                        value={values?.lostDate || ''}
                        labelName="Lost Date"
                        stateName="lostDate"
                        disabled={true}
                        error={!!(touched?.lostDate && errors?.lostDate)}
                        setFieldValue={setFieldValue}
                      />
                      <div style={{ marginTop: '10px' }}>{renderLostButton}</div>
                    </SharedStyled.FlexRow>

                    <CustomSelect
                      labelName="Lost Reason"
                      stateName="lostReason"
                      value={values?.lostReason || ''}
                      error={!!(touched?.lostReason && errors?.lostReason)}
                      setFieldValue={setFieldValue}
                      setValue={() => {}}
                      disabled={true}
                      dropDownData={
                        [
                          // '',
                          // 'Too Expensive',
                          // 'Price Shopping',
                          // 'Went With Other Provider',
                          // 'Discuss With Partner',
                          // 'Wants to Wait',
                          // 'Ghosted',
                          // 'Other (Describe in notes)',
                        ]
                      }
                      innerHeight="52px"
                      margin="10px 0 0 0"
                    />

                    <IntendWidth>
                      {values?.lostReason === 'Other (Describe in notes)' && (
                        <InputWithValidation
                          labelName="Lost Reason Notes"
                          stateName="lostNote"
                          disabled={true}
                          error={touched.lostNote && errors.lostNote ? true : false}
                        />
                      )}
                    </IntendWidth>
                  </SharedStyled.FlexCol>
                </Form>
              )
            }}
          </Formik>
        )}
      </>
    </SharedStyled.FlexCol>
  )
}

export default LeadInfo
